defmodule FamilyShareWeb.CalendarController do
  use FamilyShareWeb, :controller

  def index(conn, _params) do
    # Get current date for calendar display
    current_date = Date.utc_today()
    
    # For now, we'll use static data. In a real app, this would come from a database
    events = [
      %{
        id: 1,
        title: "Family Dinner",
        date: ~D[2025-06-15],
        time: "18:00",
        description: "Monthly family dinner at grandma's house"
      },
      %{
        id: 2,
        title: "Soccer Practice",
        date: ~D[2025-06-22],
        time: "16:00",
        description: "Kids soccer practice at the local field"
      }
    ]
    
    render(conn, :index, 
      layout: false,
      current_date: current_date,
      events: events,
      selected_date: current_date
    )
  end
end
