<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <title>Family Hub - Your Family's Digital Home</title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}></script>
  </head>
  <body class="h-full bg-gray-50">
    <.flash_group flash={@flash} />

    <!-- Mobile menu button -->
    <div class="lg:hidden fixed top-4 right-4 z-50">
      <button
        type="button"
        class="mobile-menu-button bg-white p-2 rounded-lg shadow-md border border-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        onclick="toggleMobileMenu()"
        aria-label="Toggle mobile menu"
      >
        <.icon name="hero-bars-3" class="w-6 h-6 text-gray-600" />
      </button>
    </div>

    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="lg:hidden mobile-menu-overlay menu-hidden fixed inset-0 z-40">
      <div class="fixed inset-0 bg-black bg-opacity-50" onclick="toggleMobileMenu()"></div>
      <div class="mobile-menu-sidebar fixed left-0 top-0 h-full w-80 max-w-sm">
        <.navigation_sidebar current_page="dashboard" class="h-full" />
      </div>
    </div>

    <div class="flex h-full">
      <!-- Desktop Sidebar -->
      <div class="hidden lg:block">
        <.navigation_sidebar current_page="dashboard" class="h-full" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col min-w-0">
        <!-- Header -->
        <header class="bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div class="max-w-7xl mx-auto">
            <!-- Space for mobile menu button on mobile screens -->
            <div class="lg:hidden pr-16 mb-2"></div>
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Welcome to Family Hub!</h1>
            <p class="mt-2 text-sm sm:text-base text-gray-600">
              Your central place for family organization, connection, and cherished memories.
            </p>
            <p class="mt-1 text-sm text-gray-500">
              Navigate using the sidebar or explore the features below. We're thrilled to have you and your family on board.
            </p>
          </div>
        </header>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-auto px-4 sm:px-6 lg:px-8 py-6">
          <div class="max-w-7xl mx-auto">
            <!-- Feature Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 mb-8">
              <.feature_card
                title="Family Calendar"
                description="View and manage shared events."
                icon="hero-calendar"
                button_text="Open Calendar"
                button_link="/calendar"
              />

              <.feature_card
                title="Media Sharing"
                description="Share photos and videos."
                icon="hero-photo"
                button_text="View Media"
                button_link="/media"
              />

              <.feature_card
                title="File Vault"
                description="Store important documents securely."
                icon="hero-document"
                button_text="Open Files"
                button_link="/files"
              />

              <.feature_card
                title="Shared Lists"
                description="Collaborate on to-do and shopping lists."
                icon="hero-list-bullet"
                button_text="Manage Lists"
                button_link="/lists"
              />

              <.feature_card
                title="Family Journal"
                description="Share updates and memories."
                icon="hero-book-open"
                button_text="Read Journal"
                button_link="/journal"
              />

              <.feature_card
                title="AI Invitations"
                description="Create event invitations with AI."
                icon="hero-envelope"
                button_text="Create Invitation"
                button_link="/invitations"
              />
            </div>

            <!-- Quick Stats Section -->
            <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm">
              <h2 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4 sm:mb-6">Quick Stats</h2>
              <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <.stat_card
                  title="Upcoming Events"
                  value="3"
                  subtitle="this week"
                  icon="hero-calendar"
                />

                <.stat_card
                  title="New Photos"
                  value="12"
                  subtitle="last 7 days"
                  icon="hero-photo"
                />

                <.stat_card
                  title="Shared Files"
                  value="5"
                  subtitle="files"
                  icon="hero-document"
                />

                <.stat_card
                  title="Active Lists"
                  value="2"
                  subtitle="pending items"
                  icon="hero-list-bullet"
                />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      function toggleMobileMenu() {
        const overlay = document.getElementById('mobile-menu-overlay');
        const sidebar = overlay.querySelector('.mobile-menu-sidebar');

        // Check if menu is currently hidden
        const isHidden = overlay.classList.contains('menu-hidden');

        if (isHidden) {
          // Show menu with slide animation
          overlay.classList.remove('menu-hidden');
          overlay.classList.add('menu-visible');

          // Trigger sidebar slide-in after overlay starts showing
          setTimeout(() => {
            sidebar.classList.add('menu-open');
          }, 10);

          // Prevent body scroll when menu is open
          document.body.style.overflow = 'hidden';
        } else {
          // Hide menu with slide animation
          sidebar.classList.remove('menu-open');

          // Hide overlay after sidebar finishes sliding out
          setTimeout(() => {
            overlay.classList.remove('menu-visible');
            overlay.classList.add('menu-hidden');
          }, 300); // Match CSS transition duration

          // Restore body scroll
          document.body.style.overflow = '';
        }
      }

      // Close mobile menu when clicking on navigation links
      document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('#mobile-menu-overlay a');
        navLinks.forEach(link => {
          link.addEventListener('click', function() {
            toggleMobileMenu();
          });
        });
      });
    </script>
  </body>
</html>
