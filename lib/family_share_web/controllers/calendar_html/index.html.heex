<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <title>Family Calendar - Family Hub</title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}></script>
  </head>
  <body class="h-full bg-gray-50">
    <.flash_group flash={@flash} />

    <!-- Fixed top navigation bar for mobile -->
    <div class="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200">
      <div class="flex items-center h-14 px-4">
        <button
          type="button"
          class="mobile-menu-button p-2 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset transition-colors rounded-lg"
          onclick="toggleMobileMenu()"
          aria-label="Toggle sidebar menu"
        >
          <.icon name="hero-bars-3" class="w-6 h-6 text-gray-700" />
        </button>
      </div>
    </div>

    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="lg:hidden mobile-menu-overlay menu-hidden fixed inset-0 z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50" onclick="toggleMobileMenu()"></div>
      <div class="mobile-menu-sidebar fixed left-0 top-0 h-full w-80 max-w-sm">
        <.navigation_sidebar current_page="calendar" class="h-full" />
      </div>
    </div>

    <div class="flex h-full">
      <!-- Desktop Sidebar -->
      <div class="hidden lg:block">
        <.navigation_sidebar current_page="calendar" class="h-full" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col min-w-0">
        <!-- Mobile: Scrollable content with top padding for fixed nav -->
        <div class="lg:hidden pt-14 h-full overflow-auto">
          <!-- Header section -->
          <div class="bg-white px-4 py-6 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Family Calendar</h1>
            <p class="text-gray-600">
              View, add, and manage your family's events and appointments. Click on a date to see events or add a new one.
            </p>
          </div>
          
          <!-- Mobile main content -->
          <main class="flex-1 px-4 py-6">
            <div class="max-w-7xl mx-auto space-y-6">
              <!-- Calendar Widget -->
              <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm">
                <!-- Calendar Header -->
                <div class="flex items-center justify-between mb-4">
                  <button
                    type="button"
                    class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    onclick="navigateMonth(-1)"
                  >
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <h2 class="text-lg font-semibold text-gray-900 month-year-display">
                    June 2025
                  </h2>

                  <button
                    type="button"
                    class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    onclick="navigateMonth(1)"
                  >
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>

                <!-- Calendar Grid -->
                <div class="grid grid-cols-7 gap-1 mb-2">
                  <!-- Day headers -->
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Su</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Mo</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Tu</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">We</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Th</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Fr</div>
                  <div class="text-center text-xs font-medium text-gray-500 py-2">Sa</div>
                </div>

                <div class="grid grid-cols-7 gap-1 calendar-grid">
                  <!-- Calendar days will be rendered by JavaScript -->
                </div>
              </div>

              <!-- Event Details Panel -->
              <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm event-details-container">
                <!-- Event details will be rendered by JavaScript -->
              </div>
            </div>
          </main>
        </div>
        
        <!-- Desktop: Original layout -->
        <div class="hidden lg:block flex-1 flex flex-col min-w-0">
          <!-- Desktop Header -->
          <header class="bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
            <div class="max-w-7xl mx-auto">
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Family Calendar</h1>
              <p class="mt-2 text-sm sm:text-base text-gray-600">
                View, add, and manage your family's events and appointments. Click on a date to see events or add a new one.
              </p>
            </div>
          </header>

          <!-- Desktop Main Content Area -->
          <main class="flex-1 overflow-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="max-w-7xl mx-auto">
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Calendar Widget -->
                <div class="lg:col-span-2">
                  <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm">
                    <!-- Calendar Header -->
                    <div class="flex items-center justify-between mb-4">
                      <button
                        type="button"
                        class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        onclick="navigateMonth(-1)"
                      >
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                      </button>

                      <h2 class="text-lg font-semibold text-gray-900 month-year-display">
                        June 2025
                      </h2>

                      <button
                        type="button"
                        class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        onclick="navigateMonth(1)"
                      >
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </button>
                    </div>

                    <!-- Calendar Grid -->
                    <div class="grid grid-cols-7 gap-1 mb-2">
                      <!-- Day headers -->
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Su</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Mo</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Tu</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">We</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Th</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Fr</div>
                      <div class="text-center text-xs font-medium text-gray-500 py-2">Sa</div>
                    </div>

                    <div class="grid grid-cols-7 gap-1 calendar-grid">
                      <!-- Calendar days will be rendered by JavaScript -->
                    </div>
                  </div>
                </div>

                <!-- Event Details Panel -->
                <div class="lg:col-span-1">
                  <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm event-details-container">
                    <!-- Event details will be rendered by JavaScript -->
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>

    <script>
      function toggleMobileMenu() {
        const overlay = document.getElementById('mobile-menu-overlay');
        const sidebar = overlay.querySelector('.mobile-menu-sidebar');
        
        // Check if menu is currently hidden
        const isHidden = overlay.classList.contains('menu-hidden');
        
        if (isHidden) {
          // Show menu with slide animation
          overlay.classList.remove('menu-hidden');
          overlay.classList.add('menu-visible');
          
          // Trigger sidebar slide-in after overlay starts showing
          setTimeout(() => {
            sidebar.classList.add('menu-open');
          }, 10);
          
          // Prevent body scroll when menu is open
          document.body.style.overflow = 'hidden';
        } else {
          // Hide menu with slide animation
          sidebar.classList.remove('menu-open');
          
          // Hide overlay after sidebar finishes sliding out
          setTimeout(() => {
            overlay.classList.remove('menu-visible');
            overlay.classList.add('menu-hidden');
          }, 300); // Match CSS transition duration
          
          // Restore body scroll
          document.body.style.overflow = '';
        }
      }

      // Close mobile menu when clicking on navigation links
      document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('#mobile-menu-overlay a');
        navLinks.forEach(link => {
          link.addEventListener('click', function() {
            toggleMobileMenu();
          });
        });
      });

      // Calendar state
      let currentDate = new Date();
      let selectedDate = new Date();

      // Sample events data
      const events = [
        {
          id: 1,
          title: "Family Dinner",
          date: "2025-06-15",
          time: "18:00",
          description: "Monthly family dinner at grandma's house"
        },
        {
          id: 2,
          title: "Soccer Practice",
          date: "2025-06-22",
          time: "16:00",
          description: "Kids soccer practice at the local field"
        }
      ];

      // Calendar functionality
      function navigateMonth(direction) {
        currentDate.setMonth(currentDate.getMonth() + direction);
        renderCalendar();
      }

      function selectDate(dateString) {
        selectedDate = new Date(dateString);
        renderCalendar();
        renderEventDetails();
      }

      function renderCalendar() {
        const calendarContainer = document.querySelector('.calendar-grid');
        const monthYearDisplay = document.querySelector('.month-year-display');

        if (!calendarContainer || !monthYearDisplay) return;

        // Update month/year display
        const monthNames = ["January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December"];
        monthYearDisplay.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;

        // Generate calendar days
        const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay()); // Start from Sunday

        const today = new Date();
        const calendarDays = [];

        // Generate 42 days (6 weeks)
        for (let i = 0; i < 42; i++) {
          const date = new Date(startDate);
          date.setDate(startDate.getDate() + i);

          const isCurrentMonth = date.getMonth() === currentDate.getMonth();
          const isToday = date.toDateString() === today.toDateString();
          const isSelected = date.toDateString() === selectedDate.toDateString();
          const hasEvents = events.some(event => event.date === date.toISOString().split('T')[0]);

          calendarDays.push({
            date: date,
            day: date.getDate(),
            isCurrentMonth: isCurrentMonth,
            isToday: isToday,
            isSelected: isSelected,
            hasEvents: hasEvents
          });
        }

        // Render calendar grid
        calendarContainer.innerHTML = calendarDays.map(day => `
          <div class="aspect-square">
            <button
              type="button"
              class="w-full h-full flex items-center justify-center text-sm rounded-lg transition-colors relative ${
                day.isCurrentMonth ? 'text-gray-900 hover:bg-gray-100' : 'text-gray-400'
              } ${
                day.isToday ? 'bg-blue-100 text-blue-700 font-semibold' : ''
              } ${
                day.isSelected ? 'bg-blue-600 text-white font-semibold' : ''
              } ${
                day.hasEvents ? 'font-medium' : ''
              }"
              onclick="selectDate('${day.date.toISOString().split('T')[0]}')"
            >
              ${day.day}
              ${day.hasEvents ? '<div class="absolute bottom-1 left-1/2 transform -translate-x-1/2"><div class="w-1 h-1 bg-blue-600 rounded-full"></div></div>' : ''}
            </button>
          </div>
        `).join('');
      }

      function renderEventDetails() {
        const eventContainer = document.querySelector('.event-details-container');
        if (!eventContainer) return;

        const selectedDateString = selectedDate.toISOString().split('T')[0];
        const dayEvents = events.filter(event => event.date === selectedDateString);

        const monthNames = ["January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December"];
        const formattedDate = `${monthNames[selectedDate.getMonth()]} ${selectedDate.getDate()}, ${selectedDate.getFullYear()}`;

        eventContainer.innerHTML = `
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
              Events on ${formattedDate}
            </h3>
            <button
              type="button"
              class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
              onclick="showAddEventModal()"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add New Event
            </button>
          </div>

          ${dayEvents.length === 0 ? `
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <p class="text-gray-500">No events scheduled for this day.</p>
            </div>
          ` : `
            <div class="space-y-3">
              ${dayEvents.map(event => `
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">${event.title}</h4>
                      ${event.time ? `
                        <p class="text-sm text-gray-600 mt-1">
                          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          ${event.time}
                        </p>
                      ` : ''}
                      ${event.description ? `
                        <p class="text-sm text-gray-600 mt-2">${event.description}</p>
                      ` : ''}
                    </div>
                    <div class="flex space-x-2 ml-4">
                      <button
                        type="button"
                        class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        onclick="editEvent(${event.id})"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                      <button
                        type="button"
                        class="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        onclick="deleteEvent(${event.id})"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
          `}
        `;
      }

      function showAddEventModal() {
        alert('Add Event functionality would open a modal here');
      }

      function editEvent(eventId) {
        alert(`Edit event ${eventId} functionality would open a modal here`);
      }

      function deleteEvent(eventId) {
        if (confirm('Are you sure you want to delete this event?')) {
          // Remove event from array
          const index = events.findIndex(event => event.id === eventId);
          if (index > -1) {
            events.splice(index, 1);
            renderCalendar();
            renderEventDetails();
          }
        }
      }

      // Initialize calendar when page loads
      document.addEventListener('DOMContentLoaded', function() {
        // Set initial date to June 2025 to match the design
        currentDate = new Date(2025, 5, 1); // Month is 0-indexed
        selectedDate = new Date(2025, 5, 11); // June 11th as shown in the image

        renderCalendar();
        renderEventDetails();

        // Mobile menu functionality
        const navLinks = document.querySelectorAll('#mobile-menu-overlay a');
        navLinks.forEach(link => {
          link.addEventListener('click', function() {
            toggleMobileMenu();
          });
        });
      });
    </script>
  </body>
</html>
