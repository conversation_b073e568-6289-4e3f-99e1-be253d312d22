defmodule FamilyShareWeb.AppComponents do
  @moduledoc """
  Provides application-specific UI components for Family Hub.

  These components are higher-level, domain-specific components that compose
  core UI elements to create application features like feature cards,
  navigation, and statistics displays.

  The components use Tailwind CSS with mobile-first responsive design
  principles and are built on top of the core components.
  """
  use Phoenix.Component
  use Gettext, backend: FamilyShareWeb.Gettext

  # Import core components for use in app components
  import FamilyShareWeb.CoreComponents

  @doc """
  Renders a feature card for the landing page.

  ## Examples

      <.feature_card
        title="Family Calendar"
        description="View and manage shared events."
        icon="hero-calendar"
        button_text="Open Calendar"
        button_link="/calendar"
      />
  """
  attr :title, :string, required: true
  attr :description, :string, required: true
  attr :icon, :string, required: true
  attr :button_text, :string, required: true
  attr :button_link, :string, required: true
  attr :class, :string, default: nil

  def feature_card(assigns) do
    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm hover:shadow-md transition-shadow",
      @class
    ]}>
      <div class="flex items-start space-x-3 sm:space-x-4">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <.icon name={@icon} class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">
            {@title}
          </h3>
          <p class="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
            {@description}
          </p>
          <a
            href={@button_link}
            class="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
          >
            {@button_text}
            <.icon name="hero-arrow-right" class="ml-2 w-4 h-4" />
          </a>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a stat card for displaying quick statistics.

  ## Examples

      <.stat_card
        title="Upcoming Events"
        value="3"
        subtitle="this week"
        icon="hero-calendar"
      />
  """
  attr :title, :string, required: true
  attr :value, :string, required: true
  attr :subtitle, :string, default: nil
  attr :icon, :string, required: true
  attr :class, :string, default: nil

  def stat_card(assigns) do
    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 shadow-sm",
      @class
    ]}>
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <p class="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wide">
            {@title}
          </p>
          <p class="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
            {@value}
          </p>
          <p :if={@subtitle} class="text-xs sm:text-sm text-gray-500 mt-1">
            {@subtitle}
          </p>
        </div>
        <div class="flex-shrink-0">
          <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <.icon name={@icon} class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a navigation sidebar for mobile and desktop.

  ## Examples

      <.navigation_sidebar current_page="dashboard" />
  """
  attr :current_page, :string, default: "dashboard"
  attr :class, :string, default: nil

  def navigation_sidebar(assigns) do
    ~H"""
    <nav class={[
      "bg-white border-r border-gray-200 w-full lg:w-64 flex-shrink-0",
      @class
    ]}>
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <.icon name="hero-home" class="w-5 h-5 text-white" />
          </div>
          <h1 class="text-lg font-semibold text-gray-900">Family Hub</h1>
        </div>
      </div>

      <div class="p-4">
        <ul class="space-y-1">
          <.nav_item
            icon="hero-squares-2x2"
            text="Dashboard"
            href="/"
            active={@current_page == "dashboard"}
          />
          <.nav_item
            icon="hero-calendar"
            text="Calendar"
            href="/calendar"
            active={@current_page == "calendar"}
          />
          <.nav_item
            icon="hero-photo"
            text="Media"
            href="/media"
            active={@current_page == "media"}
          />
          <.nav_item
            icon="hero-document"
            text="Files"
            href="/files"
            active={@current_page == "files"}
          />
          <.nav_item
            icon="hero-list-bullet"
            text="Lists"
            href="/lists"
            active={@current_page == "lists"}
          />
          <.nav_item
            icon="hero-book-open"
            text="Journal"
            href="/journal"
            active={@current_page == "journal"}
          />
          <.nav_item
            icon="hero-envelope"
            text="Invitations"
            href="/invitations"
            active={@current_page == "invitations"}
          />
        </ul>
      </div>
    </nav>
    """
  end

  @doc """
  Renders a navigation item for the sidebar.
  """
  attr :icon, :string, required: true
  attr :text, :string, required: true
  attr :href, :string, required: true
  attr :active, :boolean, default: false

  def nav_item(assigns) do
    ~H"""
    <li>
      <a
        href={@href}
        class={[
          "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
          @active && "bg-blue-50 text-blue-700",
          !@active && "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
        ]}
      >
        <.icon name={@icon} class={
          if @active do
            "w-5 h-5 flex-shrink-0 text-blue-600"
          else
            "w-5 h-5 flex-shrink-0 text-gray-400"
          end
        } />
        <span>{@text}</span>
      </a>
    </li>
    """
  end

  @doc """
  Renders a calendar widget with month navigation and date selection.

  ## Examples

      <.calendar_widget
        current_date={~D[2025-06-01]}
        selected_date={~D[2025-06-11]}
        events={[%{date: ~D[2025-06-15], title: "Family Dinner"}]}
      />
  """
  attr :current_date, Date, required: true
  attr :selected_date, Date, default: nil
  attr :events, :list, default: []
  attr :class, :string, default: nil

  def calendar_widget(assigns) do
    # Calculate calendar data
    assigns = assign(assigns, :calendar_data, build_calendar_data(assigns.current_date, assigns.events))

    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm",
      @class
    ]}>
      <!-- Calendar Header -->
      <div class="flex items-center justify-between mb-4">
        <button
          type="button"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          onclick="navigateMonth(-1)"
        >
          <.icon name="hero-chevron-left" class="w-5 h-5 text-gray-600" />
        </button>

        <h2 class="text-lg font-semibold text-gray-900">
          {Calendar.strftime(@current_date, "%B %Y")}
        </h2>

        <button
          type="button"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          onclick="navigateMonth(1)"
        >
          <.icon name="hero-chevron-right" class="w-5 h-5 text-gray-600" />
        </button>
      </div>

      <!-- Calendar Grid -->
      <div class="grid grid-cols-7 gap-1 mb-2">
        <!-- Day headers -->
        <div :for={day <- ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]}
             class="text-center text-xs font-medium text-gray-500 py-2">
          {day}
        </div>
      </div>

      <div class="grid grid-cols-7 gap-1">
        <div :for={day <- @calendar_data.days} class="aspect-square">
          <button
            type="button"
            class={[
              "w-full h-full flex items-center justify-center text-sm rounded-lg transition-colors relative",
              day.current_month && "text-gray-900 hover:bg-gray-100",
              !day.current_month && "text-gray-400",
              day.is_today && "bg-blue-100 text-blue-700 font-semibold",
              day.is_selected && "bg-blue-600 text-white font-semibold",
              day.has_events && "font-medium"
            ]}
            onclick={"selectDate('#{day.date}')"}
          >
            {day.day}
            <div :if={day.has_events} class="absolute bottom-1 left-1/2 transform -translate-x-1/2">
              <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
            </div>
          </button>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders an event details panel showing events for a selected date.

  ## Examples

      <.event_details_panel
        selected_date={~D[2025-06-11]}
        events={[%{title: "Family Dinner", time: "18:00", description: "..."}]}
      />
  """
  attr :selected_date, Date, required: true
  attr :events, :list, default: []
  attr :class, :string, default: nil

  def event_details_panel(assigns) do
    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm",
      @class
    ]}>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          Events on {Calendar.strftime(@selected_date, "%B %d, %Y")}
        </h3>
        <button
          type="button"
          class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
          onclick="showAddEventModal()"
        >
          <.icon name="hero-plus" class="w-4 h-4 mr-2" />
          Add New Event
        </button>
      </div>

      <div :if={Enum.empty?(@events)} class="text-center py-8">
        <.icon name="hero-calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <p class="text-gray-500">No events scheduled for this day.</p>
      </div>

      <div :if={!Enum.empty?(@events)} class="space-y-3">
        <div :for={event <- @events} class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{event.title}</h4>
              <p :if={Map.get(event, :time)} class="text-sm text-gray-600 mt-1">
                <.icon name="hero-clock" class="w-4 h-4 inline mr-1" />
                {event.time}
              </p>
              <p :if={Map.get(event, :description)} class="text-sm text-gray-600 mt-2">
                {event.description}
              </p>
            </div>
            <div class="flex space-x-2 ml-4">
              <button
                type="button"
                class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                onclick={"editEvent(#{event.id})"}
              >
                <.icon name="hero-pencil" class="w-4 h-4" />
              </button>
              <button
                type="button"
                class="p-1 text-gray-400 hover:text-red-600 transition-colors"
                onclick={"deleteEvent(#{event.id})"}
              >
                <.icon name="hero-trash" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Helper function to build calendar data
  defp build_calendar_data(current_date, events) do
    first_day = Date.beginning_of_month(current_date)

    # Get the first day of the calendar grid (might be from previous month)
    start_date = Date.add(first_day, -Date.day_of_week(first_day, :sunday))

    # Get the last day of the calendar grid (might be from next month)
    end_date = Date.add(start_date, 41) # 6 weeks * 7 days - 1

    # Create event lookup map
    event_dates = MapSet.new(events, & &1.date)

    days =
      Date.range(start_date, end_date)
      |> Enum.map(fn date ->
        %{
          date: date,
          day: date.day,
          current_month: date.month == current_date.month,
          is_today: date == Date.utc_today(),
          is_selected: false, # This would be set based on selected_date
          has_events: MapSet.member?(event_dates, date)
        }
      end)

    %{days: days}
  end
end
