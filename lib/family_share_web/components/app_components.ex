defmodule FamilyShareWeb.AppComponents do
  @moduledoc """
  Provides application-specific UI components for Family Hub.

  These components are higher-level, domain-specific components that compose
  core UI elements to create application features like feature cards,
  navigation, and statistics displays.

  The components use Tailwind CSS with mobile-first responsive design
  principles and are built on top of the core components.
  """
  use Phoenix.Component
  use Gettext, backend: FamilyShareWeb.Gettext

  # Import core components for use in app components
  import FamilyShareWeb.CoreComponents

  @doc """
  Renders a feature card for the landing page.

  ## Examples

      <.feature_card 
        title="Family Calendar" 
        description="View and manage shared events."
        icon="hero-calendar"
        button_text="Open Calendar"
        button_link="/calendar"
      />
  """
  attr :title, :string, required: true
  attr :description, :string, required: true
  attr :icon, :string, required: true
  attr :button_text, :string, required: true
  attr :button_link, :string, required: true
  attr :class, :string, default: nil

  def feature_card(assigns) do
    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 sm:p-6 shadow-sm hover:shadow-md transition-shadow",
      @class
    ]}>
      <div class="flex items-start space-x-3 sm:space-x-4">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <.icon name={@icon} class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">
            {@title}
          </h3>
          <p class="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
            {@description}
          </p>
          <a
            href={@button_link}
            class="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
          >
            {@button_text}
            <.icon name="hero-arrow-right" class="ml-2 w-4 h-4" />
          </a>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a stat card for displaying quick statistics.

  ## Examples

      <.stat_card 
        title="Upcoming Events" 
        value="3"
        subtitle="this week"
        icon="hero-calendar"
      />
  """
  attr :title, :string, required: true
  attr :value, :string, required: true
  attr :subtitle, :string, default: nil
  attr :icon, :string, required: true
  attr :class, :string, default: nil

  def stat_card(assigns) do
    ~H"""
    <div class={[
      "bg-white rounded-lg border border-gray-200 p-4 shadow-sm",
      @class
    ]}>
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <p class="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wide">
            {@title}
          </p>
          <p class="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
            {@value}
          </p>
          <p :if={@subtitle} class="text-xs sm:text-sm text-gray-500 mt-1">
            {@subtitle}
          </p>
        </div>
        <div class="flex-shrink-0">
          <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <.icon name={@icon} class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a navigation sidebar for mobile and desktop.

  ## Examples

      <.navigation_sidebar current_page="dashboard" />
  """
  attr :current_page, :string, default: "dashboard"
  attr :class, :string, default: nil

  def navigation_sidebar(assigns) do
    ~H"""
    <nav class={[
      "bg-white border-r border-gray-200 w-full lg:w-64 flex-shrink-0",
      @class
    ]}>
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <.icon name="hero-home" class="w-5 h-5 text-white" />
          </div>
          <h1 class="text-lg font-semibold text-gray-900">Family Hub</h1>
        </div>
      </div>
      
      <div class="p-4">
        <ul class="space-y-1">
          <.nav_item 
            icon="hero-squares-2x2" 
            text="Dashboard" 
            href="/" 
            active={@current_page == "dashboard"} 
          />
          <.nav_item 
            icon="hero-calendar" 
            text="Calendar" 
            href="/calendar" 
            active={@current_page == "calendar"} 
          />
          <.nav_item 
            icon="hero-photo" 
            text="Media" 
            href="/media" 
            active={@current_page == "media"} 
          />
          <.nav_item 
            icon="hero-document" 
            text="Files" 
            href="/files" 
            active={@current_page == "files"} 
          />
          <.nav_item 
            icon="hero-list-bullet" 
            text="Lists" 
            href="/lists" 
            active={@current_page == "lists"} 
          />
          <.nav_item 
            icon="hero-book-open" 
            text="Journal" 
            href="/journal" 
            active={@current_page == "journal"} 
          />
          <.nav_item 
            icon="hero-envelope" 
            text="Invitations" 
            href="/invitations" 
            active={@current_page == "invitations"} 
          />
        </ul>
      </div>
    </nav>
    """
  end

  @doc """
  Renders a navigation item for the sidebar.
  """
  attr :icon, :string, required: true
  attr :text, :string, required: true
  attr :href, :string, required: true
  attr :active, :boolean, default: false

  def nav_item(assigns) do
    ~H"""
    <li>
      <a
        href={@href}
        class={[
          "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
          @active && "bg-blue-50 text-blue-700",
          !@active && "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
        ]}
      >
        <.icon name={@icon} class={
          if @active do
            "w-5 h-5 flex-shrink-0 text-blue-600"
          else
            "w-5 h-5 flex-shrink-0 text-gray-400"
          end
        } />
        <span>{@text}</span>
      </a>
    </li>
    """
  end
end
