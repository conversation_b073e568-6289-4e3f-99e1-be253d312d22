# Family Hub Components

This directory contains the component architecture for the Family Hub application, organized into two main categories:

## Component Structure

### 1. Core Components (`core_components.ex`)

**Purpose**: Low-level, reusable UI building blocks that provide fundamental functionality.

**Components Include**:
- `button/1` - Basic button with styling variants
- `input/1` - Form input with validation support
- `modal/1` - Modal dialog with backdrop
- `flash/1` - Flash message notifications
- `icon/1` - Heroicon integration
- `header/1` - Page header component
- `table/1` - Data table with sorting
- `list/1` - Definition list component
- `back/1` - Back navigation link

**Characteristics**:
- Generic and reusable across the application
- Minimal business logic
- Focus on UI patterns and accessibility
- Can be used independently or composed together

### 2. Application Components (`app_components.ex`)

**Purpose**: Higher-level, domain-specific components that compose core components to create application features.

**Components Include**:
- `feature_card/1` - Landing page feature showcase
- `stat_card/1` - Statistics display card
- `navigation_sidebar/1` - Main navigation with mobile support
- `nav_item/1` - Individual navigation item

**Characteristics**:
- Business logic and domain-specific functionality
- Compose multiple core components
- Mobile-first responsive design
- Family Hub specific styling and behavior

## Usage

### In Templates

Both component modules are automatically imported in all Phoenix templates, LiveViews, and components:

```heex
<!-- Core components -->
<.button type="submit">Save</.button>
<.input field={@form[:email]} type="email" />

<!-- App components -->
<.feature_card 
  title="Family Calendar"
  description="Manage shared events"
  icon="hero-calendar"
  button_text="Open Calendar"
  button_link="/calendar"
/>
```

### In LiveViews and Components

```elixir
defmodule MyLiveView do
  use FamilyShareWeb, :live_view
  
  # Both CoreComponents and AppComponents are automatically imported
  
  def render(assigns) do
    ~H"""
    <.navigation_sidebar current_page="dashboard" />
    <.feature_card title="Example" ... />
    """
  end
end
```

## Design Principles

### Separation of Concerns
- **Core Components**: Focus on UI patterns, accessibility, and reusability
- **App Components**: Focus on business logic and domain-specific features

### Mobile-First Design
- All components use responsive design principles
- Touch-friendly interactions
- Progressive enhancement for larger screens

### Composability
- App components build upon core components
- Components can be easily combined and extended
- Consistent API patterns across all components

### Maintainability
- Clear separation makes it easy to:
  - Update core UI patterns without affecting business logic
  - Add new application features without modifying core components
  - Test components in isolation
  - Reuse components across different parts of the application

## Testing

Components are tested separately:

- `test/family_share_web/components/app_components_test.exs` - Tests for application components
- Core components can be tested in their own test file if needed

## Adding New Components

### Core Components
Add to `core_components.ex` if the component:
- Is generic and reusable
- Provides fundamental UI functionality
- Has no business logic
- Could be used in other applications

### Application Components  
Add to `app_components.ex` if the component:
- Is specific to Family Hub
- Contains business logic or domain knowledge
- Composes multiple core components
- Implements application-specific features

## File Structure

```
lib/family_share_web/components/
├── README.md                 # This file
├── core_components.ex        # Low-level UI components
├── app_components.ex         # Application-specific components
└── layouts/                  # Layout templates
    ├── app.html.heex
    ├── root.html.heex
    └── layouts.ex
```
