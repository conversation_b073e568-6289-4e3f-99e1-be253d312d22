@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */

/* Mobile Navigation Menu Animations */
.mobile-menu-overlay {
  transition: opacity 0.3s ease-in-out;
}

.mobile-menu-overlay.menu-hidden {
  opacity: 0;
  pointer-events: none;
}

.mobile-menu-overlay.menu-visible {
  opacity: 1;
  pointer-events: auto;
}

.mobile-menu-sidebar {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.mobile-menu-sidebar.menu-open {
  transform: translateX(0);
}

/* Mobile menu button styling */
.mobile-menu-button {
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
}

.mobile-menu-button:hover {
  background-color: #f9fafb;
}

.mobile-menu-button:active {
  background-color: #f3f4f6;
}

/* Mobile header layout improvements */
@media (max-width: 1023px) {
  .mobile-header-container {
    min-height: 64px; /* Match button height */
  }
}
