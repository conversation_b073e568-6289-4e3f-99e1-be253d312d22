@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */

/* Mobile Navigation Menu Animations */
.mobile-menu-overlay {
  transition: opacity 0.3s ease-in-out;
}

.mobile-menu-overlay.menu-hidden {
  opacity: 0;
  pointer-events: none;
}

.mobile-menu-overlay.menu-visible {
  opacity: 1;
  pointer-events: auto;
}

.mobile-menu-sidebar {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.mobile-menu-sidebar.menu-open {
  transform: translateX(0);
}

/* Ensure smooth transitions for mobile menu button */
.mobile-menu-button {
  transition: all 0.2s ease-in-out;
}

.mobile-menu-button:hover {
  transform: scale(1.05);
}

.mobile-menu-button:active {
  transform: scale(0.95);
}
