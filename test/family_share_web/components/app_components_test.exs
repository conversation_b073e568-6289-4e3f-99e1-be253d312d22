defmodule FamilyShareWeb.AppComponentsTest do
  use FamilyShareWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias FamilyShareWeb.AppComponents

  describe "feature_card/1" do
    test "renders feature card with all attributes" do
      assigns = %{
        title: "Test Feature",
        description: "Test description",
        icon: "hero-calendar",
        button_text: "Test Button",
        button_link: "/test"
      }

      html = render_component(&AppComponents.feature_card/1, assigns)

      assert html =~ "Test Feature"
      assert html =~ "Test description"
      assert html =~ "Test Button"
      assert html =~ "href=\"/test\""
      assert html =~ "hero-calendar"
    end
  end

  describe "stat_card/1" do
    test "renders stat card with required attributes" do
      assigns = %{
        title: "Test Stat",
        value: "42",
        icon: "hero-chart-bar"
      }

      html = render_component(&AppComponents.stat_card/1, assigns)

      assert html =~ "Test Stat"
      assert html =~ "42"
      assert html =~ "hero-chart-bar"
    end

    test "renders stat card with subtitle" do
      assigns = %{
        title: "Test Stat",
        value: "42",
        subtitle: "this week",
        icon: "hero-chart-bar"
      }

      html = render_component(&AppComponents.stat_card/1, assigns)

      assert html =~ "Test Stat"
      assert html =~ "42"
      assert html =~ "this week"
      assert html =~ "hero-chart-bar"
    end
  end

  describe "navigation_sidebar/1" do
    test "renders navigation sidebar with default current page" do
      assigns = %{}

      html = render_component(&AppComponents.navigation_sidebar/1, assigns)

      assert html =~ "Family Hub"
      assert html =~ "Dashboard"
      assert html =~ "Calendar"
      assert html =~ "Media"
      assert html =~ "Files"
    end

    test "renders navigation sidebar with active page" do
      assigns = %{current_page: "calendar"}

      html = render_component(&AppComponents.navigation_sidebar/1, assigns)

      assert html =~ "Family Hub"
      # Should have active styling for calendar
      assert html =~ "bg-blue-50 text-blue-700"
    end
  end

  describe "nav_item/1" do
    test "renders navigation item" do
      assigns = %{
        icon: "hero-calendar",
        text: "Calendar",
        href: "/calendar",
        active: false
      }

      html = render_component(&AppComponents.nav_item/1, assigns)

      assert html =~ "Calendar"
      assert html =~ "href=\"/calendar\""
      assert html =~ "hero-calendar"
    end

    test "renders active navigation item" do
      assigns = %{
        icon: "hero-calendar",
        text: "Calendar",
        href: "/calendar",
        active: true
      }

      html = render_component(&AppComponents.nav_item/1, assigns)

      assert html =~ "Calendar"
      assert html =~ "bg-blue-50 text-blue-700"
      assert html =~ "text-blue-600"
    end
  end
end
